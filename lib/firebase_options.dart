// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBITZKmqbNvG0XVu1Cd6Lr2DLR1Mw59MHk',
    appId: '1:49241601631:web:cdcf9b096e1c298f020dea',
    messagingSenderId: '49241601631',
    projectId: 'paysplit-app',
    authDomain: 'paysplit-app.firebaseapp.com',
    storageBucket: 'paysplit-app.firebasestorage.app',
    measurementId: 'G-4HN2W2PX3N',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDKzZgcP9y4kevoF2XXE9XP65GDIoKJU44',
    appId: '1:49241601631:android:1ae6face356532c8020dea',
    messagingSenderId: '49241601631',
    projectId: 'paysplit-app',
    storageBucket: 'paysplit-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA2JTY2pR3aFK8DGpxkhyh_MqW-uAZf2fA',
    appId: '1:49241601631:ios:605e685bfb9eb95a020dea',
    messagingSenderId: '49241601631',
    projectId: 'paysplit-app',
    storageBucket: 'paysplit-app.firebasestorage.app',
    androidClientId: '49241601631-e9ur8h602d96hbvegqnd6t3c6nadjd2s.apps.googleusercontent.com',
    iosClientId: '49241601631-5p16a9mqo4045g2dni5gl0npdh8f8vu2.apps.googleusercontent.com',
    iosBundleId: 'com.paysplit.paysplit',
  );
}
