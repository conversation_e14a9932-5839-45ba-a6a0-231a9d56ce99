<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>49241601631-5p16a9mqo4045g2dni5gl0npdh8f8vu2.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.49241601631-5p16a9mqo4045g2dni5gl0npdh8f8vu2</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>49241601631-e9ur8h602d96hbvegqnd6t3c6nadjd2s.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyA2JTY2pR3aFK8DGpxkhyh_MqW-uAZf2fA</string>
	<key>GCM_SENDER_ID</key>
	<string>49241601631</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.paysplit.paysplit</string>
	<key>PROJECT_ID</key>
	<string>paysplit-app</string>
	<key>STORAGE_BUCKET</key>
	<string>paysplit-app.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:49241601631:ios:605e685bfb9eb95a020dea</string>
</dict>
</plist>