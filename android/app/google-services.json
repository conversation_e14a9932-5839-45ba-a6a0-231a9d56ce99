{"project_info": {"project_number": "49241601631", "project_id": "paysplit-app", "storage_bucket": "paysplit-app.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:49241601631:android:2658e09d13ded7fd020dea", "android_client_info": {"package_name": "com.example.paysplit_app"}}, "oauth_client": [{"client_id": "49241601631-td20kfmghj6pvdvdvucgujkchgto9fms.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.paysplit_app", "certificate_hash": "0eea891114cb479329aca11ea66b25359167a4af"}}, {"client_id": "49241601631-81m918ht9mc01p106sb40gnjv57f030t.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDKzZgcP9y4kevoF2XXE9XP65GDIoKJU44"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "49241601631-81m918ht9mc01p106sb40gnjv57f030t.apps.googleusercontent.com", "client_type": 3}, {"client_id": "49241601631-jfckr4a59ij43it7vpp5d2oterglmu18.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.paysplitApp"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:49241601631:android:1ae6face356532c8020dea", "android_client_info": {"package_name": "com.paysplit.paysplit"}}, "oauth_client": [{"client_id": "49241601631-81m918ht9mc01p106sb40gnjv57f030t.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDKzZgcP9y4kevoF2XXE9XP65GDIoKJU44"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "49241601631-81m918ht9mc01p106sb40gnjv57f030t.apps.googleusercontent.com", "client_type": 3}, {"client_id": "49241601631-jfckr4a59ij43it7vpp5d2oterglmu18.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.paysplitApp"}}]}}}], "configuration_version": "1"}